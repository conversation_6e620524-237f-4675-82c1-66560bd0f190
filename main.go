package main

import (
	"strconv"
	"strings"

	"token_replace/errs"
	"token_replace/logic"

	"git.woa.com/bkdevops/golang-atom-sdk/api"
	"git.woa.com/bkdevops/golang-atom-sdk/log"
)

func main() {
	defer func() {
		if err := recover(); err != nil {
			log.Error("panic: ", err)
			api.FinishBuild(api.StatusError, "panic occurs")
		}
	}()

	output, err := logic.Process(loadInputParams())
	if err != nil {
		api.FinishBuildWithErrorCode(api.StatusFailure, err.Error(), errs.CodeUnknown)
	}
	log.Infof("output: %+v", output)

	api.FinishBuild(api.StatusSuccess, "执行成功")
}

// loadInputParams 加载输入参数，同时处理默认值等逻辑
func loadInputParams() logic.InputParams {
	p := logic.InputParams{
		FileNames:       CleanAndSplit(api.GetInputParam("file_names")),
		HandelPath:      api.GetInputParam("handel_path"),
		EnableRecursion: ConvertStringToBool(api.GetInputParam("enable_recursion")),
		EnableBak:       ConvertStringToBool(api.GetInputParam("enable_bak")),
		Params:          logic.UnmarshalParams(api.GetInputParam("params")),
	}
	log.Infof("InputParams: %+v", p)
	log.Infof("GetWorkspace: %+v", api.GetWorkspace())
	return p
}

// loadInputParamWithDefault 带默认值读取配置
func loadInputParamWithDefault(key string, defaultValue string) string {
	s := api.GetInputParam(key)
	if s == "" {
		return defaultValue
	}
	return s
}

// ConvertStringToInt 将字符串转换为整数
func ConvertStringToInt(s string) int {
	// 使用 strconv.Atoi 将字符串转换为整数
	num, err := strconv.Atoi(s)
	if err != nil {
		log.Errorf("转换数值失败：%v", err)
		return 0
	}
	return num // 返回转换后的整数
}

// ConvertStringToBool 将字符串转换为布尔值
func ConvertStringToBool(s string) bool {
	// 使用 strings.ToLower 将字符串转换为小写
	switch strings.ToLower(s) {
	case "true":
		return true
	case "false":
		return false
	default:
		return false
	}
}

// CleanAndSplit 将字符串按逗号分割，并清理空格和全角字符
func CleanAndSplit(s string) []string {
	// 替换全角逗号为半角逗号
	s = strings.ReplaceAll(s, "，", ",")
	// 按逗号分割字符串
	parts := strings.Split(s, ",")
	// 创建一个切片来存储清理后的结果
	var result []string
	for _, part := range parts {
		// 清理前后空格
		cleaned := strings.TrimSpace(part)
		// 如果清理后的字符串不为空，则添加到结果切片中
		if cleaned != "" {
			result = append(result, cleaned)
		}
	}

	return result
}
